'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import AnimatedCard from '@/components/AnimatedCard'
import { LoadingButton } from '@/components/LoadingSpinner'
import { useNotifications } from '@/components/AnimatedNotification'
import { LazyCharts } from '@/components/optimization/LazyLoader'
import { LazyOnScroll } from '@/components/optimization/LazyLoader'
import PerformanceMonitor from '@/components/optimization/PerformanceMonitor'
import {
  Package,
  Users,
  CreditCard,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Clock,
  DollarSign,
  ArrowUpRight,
  Activity,
  Calendar,
  Zap,
  Target,
  BarChart3,
  PieChart
} from 'lucide-react'

interface DashboardStats {
  totalProducts: number
  totalCustomers: number
  totalDebts: number
  totalRevenue: number
}

interface RecentActivity {
  id: string
  type: 'debt' | 'payment' | 'product'
  description: string
  amount?: number
  timestamp: string
}

interface PaymentWithCustomer {
  id: string
  amount: number
  dateOfPayment: string
  customer: {
    firstName: string
    lastName: string
  }
}

interface DebtWithCustomer {
  id: string
  totalAmount: number
  dateOfDebt: string
  customer: {
    firstName: string
    lastName: string
  }
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalCustomers: 0,
    totalDebts: 0,
    totalRevenue: 0,
  })
  const [loading, setLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { showSuccess, showError } = useNotifications()

  // Sample chart data - In production, this would come from your API
  const salesData = [
    { date: 'Mon', sales: 85, target: 100, revenue: 2100 },
    { date: 'Tue', sales: 92, target: 100, revenue: 2300 },
    { date: 'Wed', sales: 78, target: 100, revenue: 1950 },
    { date: 'Thu', sales: 95, target: 100, revenue: 2375 },
    { date: 'Fri', sales: 88, target: 100, revenue: 2200 },
    { date: 'Sat', sales: 110, target: 100, revenue: 2750 },
    { date: 'Sun', sales: 75, target: 100, revenue: 1875 },
  ]

  const revenueData = [
    { month: 'Jan', revenue: 45000, profit: 12000, expenses: 33000 },
    { month: 'Feb', revenue: 52000, profit: 15000, expenses: 37000 },
    { month: 'Mar', revenue: 48000, profit: 13500, expenses: 34500 },
    { month: 'Apr', revenue: 61000, profit: 18000, expenses: 43000 },
    { month: 'May', revenue: 55000, profit: 16500, expenses: 38500 },
    { month: 'Jun', revenue: 67000, profit: 20000, expenses: 47000 },
  ]

  const inventoryData = [
    { category: 'Beverages', value: 150, status: 'in-stock' as const, color: '#3b82f6' },
    { category: 'Snacks', value: 89, status: 'low-stock' as const, color: '#f59e0b' },
    { category: 'Household', value: 45, status: 'in-stock' as const, color: '#10b981' },
    { category: 'Personal Care', value: 23, status: 'out-of-stock' as const, color: '#ef4444' },
    { category: 'Condiments', value: 67, status: 'in-stock' as const, color: '#8b5cf6' },
  ]

  const customerData = [
    { month: 'Jan', newCustomers: 12, activeCustomers: 45, churnedCustomers: 2, satisfaction: 4.2 },
    { month: 'Feb', newCustomers: 18, activeCustomers: 58, churnedCustomers: 3, satisfaction: 4.3 },
    { month: 'Mar', newCustomers: 15, activeCustomers: 67, churnedCustomers: 1, satisfaction: 4.5 },
    { month: 'Apr', newCustomers: 22, activeCustomers: 78, churnedCustomers: 4, satisfaction: 4.4 },
    { month: 'May', newCustomers: 19, activeCustomers: 85, churnedCustomers: 2, satisfaction: 4.6 },
    { month: 'Jun', newCustomers: 25, activeCustomers: 95, churnedCustomers: 3, satisfaction: 4.7 },
  ]

  // Enhanced data fetching with auto-refresh
  const fetchStats = async (isRefresh = false) => {
    if (isRefresh) {
      setIsRefreshing(true)
    } else {
      setLoading(true)
    }

    try {
      // Fetch all data concurrently for better performance
      const [productsRes, customersRes, debtsRes, paymentsRes] = await Promise.all([
        fetch('/api/products'),
        fetch('/api/customers'),
        fetch('/api/debts'),
        fetch('/api/payments')
      ])

      const [products, customers, debts, payments] = await Promise.all([
        productsRes.json(),
        customersRes.json(),
        debtsRes.json(),
        paymentsRes.json()
      ])

      const totalRevenue = payments.reduce((sum: number, payment: PaymentWithCustomer) => sum + payment.amount, 0)
      const totalDebts = debts.reduce((sum: number, debt: DebtWithCustomer) => sum + debt.totalAmount, 0)

      setStats({
        totalProducts: products.length,
        totalCustomers: customers.length,
        totalDebts,
        totalRevenue,
      })

      // Generate recent activity with better sorting
      const activities: RecentActivity[] = [
        ...payments.slice(0, 3).map((payment: PaymentWithCustomer) => ({
          id: payment.id,
          type: 'payment' as const,
          description: `Payment received from ${payment.customer.firstName} ${payment.customer.lastName}`,
          amount: payment.amount,
          timestamp: payment.dateOfPayment,
        })),
        ...debts.slice(0, 2).map((debt: DebtWithCustomer) => ({
          id: debt.id,
          type: 'debt' as const,
          description: `New debt recorded for ${debt.customer.firstName} ${debt.customer.lastName}`,
          amount: debt.totalAmount,
          timestamp: debt.dateOfDebt,
        })),
      ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5)

      setRecentActivity(activities)
      setLastUpdated(new Date())

      if (isRefresh) {
        showSuccess('Data Updated', 'Dashboard data has been refreshed successfully!')
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      if (isRefresh) {
        showError('Update Failed', 'Failed to refresh dashboard data. Please try again.')
      }
    } finally {
      setLoading(false)
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    fetchStats()

    // Auto-refresh every 5 minutes
    const interval = setInterval(() => {
      fetchStats(true)
    }, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  // Manual refresh function
  const handleRefresh = () => {
    fetchStats(true)
  }

  const statCards = [
    {
      title: 'Total Products',
      value: stats.totalProducts,
      icon: Package,
      gradient: 'from-blue-500 via-blue-600 to-blue-700',
      bgColor: 'bg-gradient-to-br from-blue-50 to-blue-100',
      textColor: 'text-blue-700',
      iconBg: 'bg-gradient-to-br from-blue-500 to-blue-600',
      change: '+12%',
      changeType: 'positive' as const,
      trend: [65, 78, 82, 95, 88, 92, 98],
      description: 'Active inventory items',
    },
    {
      title: 'Total Customers',
      value: stats.totalCustomers,
      icon: Users,
      gradient: 'from-emerald-500 via-emerald-600 to-emerald-700',
      bgColor: 'bg-gradient-to-br from-emerald-50 to-emerald-100',
      textColor: 'text-emerald-700',
      iconBg: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
      change: '+8%',
      changeType: 'positive' as const,
      trend: [45, 52, 48, 61, 55, 67, 73],
      description: 'Registered customers',
    },
    {
      title: 'Outstanding Debts',
      value: `₱${stats.totalDebts.toLocaleString()}`,
      icon: CreditCard,
      gradient: 'from-amber-500 via-orange-500 to-red-500',
      bgColor: 'bg-gradient-to-br from-amber-50 to-orange-100',
      textColor: 'text-orange-700',
      iconBg: 'bg-gradient-to-br from-amber-500 to-orange-500',
      change: '-5%',
      changeType: 'negative' as const,
      trend: [85, 78, 82, 75, 68, 72, 65],
      description: 'Pending collections',
    },
    {
      title: 'Total Revenue',
      value: `₱${stats.totalRevenue.toLocaleString()}`,
      icon: TrendingUp,
      gradient: 'from-violet-500 via-purple-600 to-purple-700',
      bgColor: 'bg-gradient-to-br from-violet-50 to-purple-100',
      textColor: 'text-purple-700',
      iconBg: 'bg-gradient-to-br from-violet-500 to-purple-600',
      change: '+23%',
      changeType: 'positive' as const,
      trend: [120, 135, 148, 162, 158, 175, 189],
      description: 'Monthly earnings',
    },
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <DollarSign className="h-4 w-4 text-green-600" />
      case 'debt':
        return <CreditCard className="h-4 w-4 text-orange-600" />
      case 'product':
        return <Package className="h-4 w-4 text-blue-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <DashboardLayout
      title="Dashboard"
      subtitle={`Welcome back! Here's what's happening with your store today.${lastUpdated ? ` • Last updated: ${lastUpdated.toLocaleTimeString()}` : ''}`}
      actions={
        <div className="flex items-center space-x-2 sm:space-x-3">
          <LoadingButton
            loading={isRefreshing}
            onClick={handleRefresh}
            variant="secondary"
            className="animate-fade-in stagger-1"
          >
            <Activity className="h-4 w-4" />
            <span className="hidden sm:inline">Refresh</span>
          </LoadingButton>
          <button className="btn-primary btn-interactive animate-fade-in stagger-2">
            <span className="hidden sm:inline">Quick Sale</span>
            <span className="sm:hidden">Sale</span>
          </button>
        </div>
      }
    >
      {/* Professional Stats Cards - Top Section */}
      <div className="dashboard-section mobile-content animate-fade-in stagger-1">
        <div className="stats-grid">
          {statCards.map((card, index) => (
            <AnimatedCard
              key={index}
              title={card.title}
              value={card.value}
              description={card.description}
              icon={card.icon}
              gradient={card.gradient}
              bgColor={card.bgColor}
              textColor={card.textColor}
              iconBg={card.iconBg}
              change={card.change}
              changeType={card.changeType}
              trend={card.trend}
              delay={index * 100}
              loading={loading}
            />
          ))}
        </div>
      </div>

      {/* Professional Dashboard Grid - Aligned Cards */}
      <div className="dashboard-section dashboard-container mobile-content animate-fade-in stagger-2">
        <div className="dashboard-grid">
          {/* Enhanced Quick Actions - Professional Alignment */}
          <div className="dashboard-grid-item order-2 lg:order-1">
            <div className="dashboard-card">
              <div className="dashboard-card-header">
                <div>
                  <h3 className="dashboard-card-title">Quick Actions</h3>
                  <p className="dashboard-card-subtitle">Streamline your daily operations</p>
                </div>
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg flex-shrink-0">
                  <Zap className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <a
                  href="/products/new"
                  className="group relative block p-5 bg-gradient-to-br from-blue-50 via-blue-100 to-indigo-100 hover:from-blue-100 hover:via-blue-200 hover:to-indigo-200 rounded-2xl transition-all duration-300 border border-blue-200 hover:border-blue-300 hover-lift overflow-hidden animate-scale-in stagger-1"
                >
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                  <div className="relative flex items-center space-x-4">
                    <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                      <Package className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="font-bold text-blue-900 text-lg">Add Product</div>
                      <div className="text-sm text-blue-700 mt-1">Expand your inventory</div>
                    </div>
                    <ArrowUpRight className="h-5 w-5 text-blue-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                  </div>
                </a>

                <a
                  href="/customers/new"
                  className="group relative block p-5 bg-gradient-to-br from-emerald-50 via-emerald-100 to-green-100 hover:from-emerald-100 hover:via-emerald-200 hover:to-green-200 rounded-2xl transition-all duration-300 border border-emerald-200 hover:border-emerald-300 hover-lift overflow-hidden animate-scale-in stagger-2"
                >
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                  <div className="relative flex items-center space-x-4">
                    <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="font-bold text-emerald-900 text-lg">Add Customer</div>
                      <div className="text-sm text-emerald-700 mt-1">Register new customer</div>
                    </div>
                    <ArrowUpRight className="h-5 w-5 text-emerald-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                  </div>
                </a>

                <a
                  href="/debts/new"
                  className="group relative block p-5 bg-gradient-to-br from-amber-50 via-orange-100 to-red-100 hover:from-amber-100 hover:via-orange-200 hover:to-red-200 rounded-2xl transition-all duration-300 border border-orange-200 hover:border-orange-300 hover-lift overflow-hidden animate-scale-in stagger-3"
                >
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                  <div className="relative flex items-center space-x-4">
                    <div className="bg-gradient-to-br from-orange-500 to-red-500 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                      <CreditCard className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="font-bold text-orange-900 text-lg">Record Debt</div>
                      <div className="text-sm text-orange-700 mt-1">Track customer debt</div>
                    </div>
                    <ArrowUpRight className="h-5 w-5 text-orange-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                  </div>
                </a>

                <a
                  href="/payments/new"
                  className="group relative block p-5 bg-gradient-to-br from-purple-50 via-purple-100 to-indigo-100 hover:from-purple-100 hover:via-purple-200 hover:to-indigo-200 rounded-2xl transition-all duration-300 border border-purple-200 hover:border-purple-300 hover-lift overflow-hidden animate-scale-in stagger-4"
                >
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                  <div className="relative flex items-center space-x-4">
                    <div className="bg-gradient-to-br from-purple-500 to-indigo-500 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                      <DollarSign className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="font-bold text-purple-900 text-lg">Record Payment</div>
                      <div className="text-sm text-purple-700 mt-1">Process customer payment</div>
                    </div>
                    <ArrowUpRight className="h-5 w-5 text-purple-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                  </div>
                </a>
              </div>
            </div>
          </div>

          {/* Enhanced Recent Activity - Professional Alignment */}
          <div className="dashboard-grid-item order-1 lg:order-2">
            <div className="dashboard-card">
              <div className="dashboard-card-header">
                <div>
                  <h3 className="dashboard-card-title">Recent Activity</h3>
                  <p className="dashboard-card-subtitle">Latest store operations</p>
                </div>
                <div className="bg-gradient-to-r from-green-500 to-blue-600 p-2 rounded-lg flex-shrink-0">
                  <Activity className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="space-y-3">
                {loading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                        <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : recentActivity.length > 0 ? (
                  recentActivity.map((activity) => (
                    <div key={activity.id} className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 rounded-xl transition-all duration-300 border border-transparent hover:border-blue-200">
                      <div className="flex-shrink-0 mt-1 p-2 bg-white rounded-lg shadow-sm group-hover:shadow-md transition-shadow duration-300">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-semibold text-gray-900 truncate group-hover:text-blue-900 transition-colors duration-300">
                          {activity.description}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-xs text-gray-500 flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatDate(activity.timestamp)}</span>
                          </p>
                          {activity.amount && (
                            <p className="text-sm font-bold text-gray-900 bg-white px-2 py-1 rounded-lg shadow-sm">
                              ₱{activity.amount.toLocaleString()}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <ArrowUpRight className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                      <Activity className="h-8 w-8 text-gray-400" />
                    </div>
                    <p className="text-sm text-gray-500">No recent activity to display</p>
                    <p className="text-xs text-gray-400 mt-1">Activity will appear here as you use the system</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
