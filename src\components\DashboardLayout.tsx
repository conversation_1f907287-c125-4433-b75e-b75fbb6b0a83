'use client'

import Navigation from './Navigation'
import { NotificationContainer } from './AnimatedNotification'
import SmartSearch from './search/SmartSearch'
import Breadcrumb from './navigation/Breadcrumb'
import NavigationHistory from './navigation/NavigationHistory'
import { Bell, Search, User, Calendar, Clock, ChevronDown } from 'lucide-react'
import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
}

// Page titles and subtitles mapping
const pageInfo: Record<string, { title: string; subtitle: string }> = {
  '/dashboard': { title: 'Dashboard', subtitle: 'Overview & analytics' },
  '/products': { title: 'Products', subtitle: 'Manage your store inventory • 5 products' },
  '/customers': { title: 'Customers', subtitle: 'Customer database' },
  '/debts': { title: 'Debts', subtitle: 'Track customer debts' },
  '/payments': { title: 'Payments', subtitle: 'Payment records' },
  '/reports': { title: 'Reports', subtitle: 'Business insights' },
}

export default function DashboardLayout({
  children,
  title,
  subtitle,
  actions
}: DashboardLayoutProps) {
  const pathname = usePathname()
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const [showSearch, setShowSearch] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Get current page info if not provided
  const currentPageInfo = pageInfo[pathname] || { title: 'Dashboard', subtitle: 'Overview & analytics' }
  const pageTitle = title || currentPageInfo.title
  const pageSubtitle = subtitle || currentPageInfo.subtitle

  // Initialize client-side only
  useEffect(() => {
    setIsClient(true)
    setCurrentTime(new Date())

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-dropdown]')) {
        setShowNotifications(false)
        setShowProfile(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const formatTime = (date: Date | null) => {
    if (!date || !isClient) return '--:--'
    try {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return '--:--'
    }
  }

  const formatDate = (date: Date | null) => {
    if (!date || !isClient) return 'Loading...'
    try {
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return 'Loading...'
    }
  }

  return (
    <div className="dashboard-layout min-h-screen bg-gray-50 flex">
      {/* Navigation Sidebar - Sticky Container */}
      <div className="flex-shrink-0">
        <Navigation
          isCollapsed={isCollapsed}
          onToggleCollapse={() => setIsCollapsed(!isCollapsed)}
          isMobileMenuOpen={isMobileMenuOpen}
          onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        />
      </div>



      {/* Main Content Area - Professional Flex Layout */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Header - Sticky */}
        <div className="sticky top-0 z-20 bg-white border-b border-gray-200 shadow-sm">
          <div className="px-4 lg:px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Page Title Section */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-4">
                  {/* Mobile Menu Button */}
                  <button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                  >
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </button>

                  {/* Desktop Sidebar Toggle Button */}
                  <button
                    onClick={() => setIsCollapsed(!isCollapsed)}
                    className="hidden lg:flex p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                  >
                    {isCollapsed ? (
                      <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7M19 19l-7-7 7-7" />
                      </svg>
                    )}
                  </button>

                  <div className="flex-1 min-w-0">
                    <h1 className="text-2xl font-bold text-gray-900 truncate">
                      {pageTitle}
                    </h1>
                    {pageSubtitle && (
                      <p className="text-sm text-gray-600 mt-1 truncate">
                        {pageSubtitle}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Header Actions */}
              <div className="flex items-center space-x-3">
                {/* Search */}
                <div className="relative">
                  <button
                    onClick={() => setShowSearch(!showSearch)}
                    className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                  >
                    <Search className="h-5 w-5" />
                  </button>

                  {showSearch && (
                    <div className="absolute top-full right-0 mt-2 w-80 z-50">
                      <SmartSearch onClose={() => setShowSearch(false)} />
                    </div>
                  )}
                </div>

                {/* Notifications */}
                <div className="relative">
                  <button
                    onClick={() => setShowNotifications(!showNotifications)}
                    className="relative p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                  >
                    <Bell className="h-5 w-5" />
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      3
                    </span>
                  </button>

                  {showNotifications && (
                    <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-xl z-50 animate-fade-in">
                      <div className="p-4 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
                      </div>
                      <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
                        <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                          <div className="bg-blue-500 p-1 rounded-full">
                            <Bell className="h-3 w-3 text-white" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">Low Stock Alert</p>
                            <p className="text-xs text-gray-600 mt-1">5 products are running low</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                          <div className="bg-green-500 p-1 rounded-full">
                            <Bell className="h-3 w-3 text-white" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">Payment Received</p>
                            <p className="text-xs text-gray-600 mt-1">₱2,500 from Juan Dela Cruz</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Profile */}
                <div className="relative">
                  <button
                    onClick={() => setShowProfile(!showProfile)}
                    className="flex items-center space-x-2 p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                  >
                    <User className="h-5 w-5" />
                    <ChevronDown className="h-4 w-4" />
                  </button>

                  {showProfile && (
                    <div className="absolute top-full right-0 mt-2 w-48 bg-white border border-gray-200 rounded-xl shadow-xl z-50 animate-fade-in">
                      <div className="p-4 border-b border-gray-200">
                        <p className="text-sm font-medium text-gray-900">Admin User</p>
                        <p className="text-xs text-gray-600"><EMAIL></p>
                      </div>
                      <div className="p-2">
                        <button className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg">
                          Settings
                        </button>
                        <button className="w-full flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg">
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Custom Actions */}
                {actions && (
                  <div className="flex items-center space-x-2">
                    {actions}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-6 bg-gray-50">
          <div className="w-full max-w-full overflow-x-hidden">
            {/* Breadcrumb Navigation */}
            <div className="mb-6">
              <Breadcrumb className="animate-fade-in" />
            </div>

            {/* Content Container */}
            <div className="w-full">
              {children}
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200 px-4 lg:px-6 py-4 mt-auto">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span>&copy; 2024 Caparan Tindahan</span>
              <span className="hidden md:inline">•</span>
              <span className="hidden md:inline">Version 1.0.0</span>
            </div>
            <div className="flex items-center space-x-4 mt-2 md:mt-0">
              <span className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                System Online
              </span>
              <span className="hidden md:inline">•</span>
              <span className="hidden md:inline">Last backup: Today 3:00 AM</span>
            </div>
          </div>
        </footer>
      </div>

      {/* Notification Container */}
      <NotificationContainer />
    </div>
  )
}
